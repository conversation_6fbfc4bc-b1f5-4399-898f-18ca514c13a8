/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { FormProvider, useForm, Controller } from 'react-hook-form';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import PlanAndAreaUnit from './PlanAndAreaUnit';
import SelectGateKeepers from './SelectGateKeepers';
import { ButtonType, FormFieldsCheckboxOption, FormFields as FormFieldsType, FormItem, InputType } from 'types/Form';
import classNames from 'classnames';
import AimsSystem from './AimsSystem';
import ESmartIsoSystem from './ESmartIsoSystem';
import PhaDatabase from './PhaDatabase';
import FileUpload from './FileUpload';
import { AlertCircle } from 'react-feather';
import ModalConfirm from 'components/partials/ModalConfirm';
import ModalReasonConfirm from '../../task/components/ModalReasonConfirm';
import { COMMON_MESSAGE, QUERY_KEY } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { UPDATE_WORKFLOW_INSTANCE, WORKFLOW_INSTANCE_CREATE } from 'services/WorkflowService';
import { getStatusBadgeStep, isDateValid, showToast } from 'utils/common';
import { useAuthStore } from 'stores/authStore';
import { COMPLETE_TASK } from 'services/TaskService';
import { TASK_COMMENT_CREATE } from 'services/TaskCommentService';
import { UserTaskEntity } from 'types/Workflow';
import { useAppStore } from 'stores/appStore';
import PreviousFormFields from './PreviousFormFields';
import { Task, TaskStatus } from 'types/Task';
import { useClickOutside } from 'hooks/useClickOutSide';
import { DateRangePicker as RsuiteDateRangePicker } from 'rsuite';
import 'rsuite/DateRangePicker/styles/index.css';
import SelectGateKeepersReviewWrapper from './SelectGateKeepersReviewWrapper';
import 'react-quill/dist/quill.snow.css';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { UserListAllQuery, UserQueryVariables } from 'types/User';
import { USER_LIST_ALL } from 'services/UserService';
import { keepPreviousData } from '@tanstack/react-query';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { TableReview } from 'components/partials/TableReview';

interface FormFieldsProps {
    dataUserTaskDetail?: Task;
    initialData?: any | null;
    schemaForm: FormItem;
    isDisableForm?: boolean;
    dataPlanAndAreaUnit?: any;
    listDataForm?: UserTaskEntity[];
    assignee?: string;
    isPreview?: boolean;
}
export default function FormFields({
    dataUserTaskDetail,
    initialData,
    schemaForm,
    isDisableForm = false,
    dataPlanAndAreaUnit,
    listDataForm,
    assignee,
    isPreview,
}: FormFieldsProps) {
    const { id } = useParams<{ id: string }>();
    const user = useAuthStore((state) => state.user);
    const navigate = useNavigate();
    const location = useLocation();
    const isTaskEditRoute = location.pathname.startsWith('/task/edit');
    const isTaskViewRoute = location.pathname.startsWith('/task/view');
    const isChangeRequestEditRoute = location.pathname.startsWith('/changeRequest/edit');
    const isChangeRequestAddRoute = location.pathname.startsWith('/changeRequest/add');
    const isChangeRequestViewRoute = location.pathname.startsWith('/changeRequest/view');
    const isEditFormRoute = location.pathname.startsWith('/form/edit');
    const isFormAddRoute = location.pathname.startsWith('/form/add');
    const isWorkflowInstanceRoute = location.pathname.startsWith('/workflowInstance');

    const wrapperRef = useRef<HTMLDivElement>(null);

    const activeFields = useAppStore((state) => state.activeFields);
    const setActiveFields = useAppStore((state) => state.setActiveFields);
    const setListOption = useAppStore((state) => state.setListOption);
    const setActiveOption = useAppStore((state) => state.setActiveOption);

    const methods = useForm({
        shouldUnregister: true,
        defaultValues: initialData || {},
    });
    const { register, handleSubmit, reset, unregister, getValues, control, watch, setValue } = methods;
    const [isAlert, setIsAlert] = useState(false);
    const actionTypeRef = useRef<string | null>(null);
    const [loadingButton, setLoadingButton] = useState<ButtonType | null>(null);

    // State for ModalReasonConfirm
    const [showReasonModal, setShowReasonModal] = useState(false);
    const [reasonActionType, setReasonActionType] = useState<'terminate' | 'reject' | null>(null);

    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    // Get all keyRequired fields to watch for changes
    const keyRequiredFields =
        schemaForm?.form_fields
            ?.filter(
                (field) =>
                    field.type === 'text_area' &&
                    field.keyRequired &&
                    field.valueRequired &&
                    field.valueRequired.length > 0
            )
            ?.map((field) => field.keyRequired)
            ?.filter((key): key is string => !!key) || [];

    // Watch for changes in keyRequired fields
    const watchedValues = watch(keyRequiredFields);

    // Get table fields to watch for real-time validation
    const tableFields =
        schemaForm?.form_fields
            ?.filter((field) => ['aims_system', 'e_smart_iso_system', 'pha_database'].includes(field.type))
            ?.map((field) => field.key) || [];

    // Watch for changes in table fields
    const watchedTableValues = watch(tableFields);

    // Clear validation errors for textarea when conditional required is no longer met
    useEffect(() => {
        const textareaFields =
            schemaForm?.form_fields?.filter(
                (field) =>
                    field.type === 'text_area' &&
                    field.keyRequired &&
                    field.valueRequired &&
                    field.valueRequired.length > 0
            ) || [];

        let hasChanges = false;
        const newErrors = { ...validationErrors };

        textareaFields.forEach((field) => {
            const isCurrentlyRequired = isFieldConditionallyRequired(field, watch());

            // If field was required but now is not, clear its error
            if (!isCurrentlyRequired && validationErrors[field.key]) {
                delete newErrors[field.key];
                hasChanges = true;
            }
        });

        if (hasChanges) {
            setValidationErrors(newErrors);
        }
    }, [watchedValues, schemaForm?.form_fields]);

    // Clear validation errors for table fields when all rows are filled completely
    useEffect(() => {
        const tableFieldsConfig =
            schemaForm?.form_fields?.filter((field) =>
                ['aims_system', 'e_smart_iso_system', 'pha_database'].includes(field.type)
            ) || [];

        let hasChanges = false;
        const newErrors = { ...validationErrors };

        tableFieldsConfig.forEach((field) => {
            const fieldValue = watch(field.key);
            let isValid = false;

            // Validate based on field type
            switch (field.type) {
                case 'aims_system':
                    isValid = validateAimsSystemField(fieldValue);
                    break;
                case 'e_smart_iso_system':
                    isValid = validateESmartIsoSystemField(fieldValue);
                    break;
                case 'pha_database':
                    isValid = validatePhaDatabaseField(fieldValue);
                    break;
                default:
                    isValid = false;
                    break;
            }

            // If field is now valid and had an error, clear the error
            if (isValid && validationErrors[field.key]) {
                delete newErrors[field.key];
                hasChanges = true;
            }
        });

        if (hasChanges) {
            setValidationErrors(newErrors);
        }
    }, [watchedTableValues, schemaForm?.form_fields, validationErrors]);

    // State for managing date range inputs
    const [dateRangeStates, setDateRangeStates] = useState<
        Record<string, { startDate: Date | null; endDate: Date | null }>
    >({});

    const updateProcessMutation = useGraphQLMutation(UPDATE_WORKFLOW_INSTANCE, '', {
        onSuccess: () => {
            showToast(true, ['Update process successfully']);
            navigate('/changeRequest');
        },
        onSettled: () => {
            setLoadingButton(null);
        },
    });

    const completeTaskMutation = useGraphQLMutation(COMPLETE_TASK, '', {
        onSuccess: () => {
            showToast(true, ['Complete task successfully']);
            navigate('/task');
        },
        onSettled: () => {
            setLoadingButton(null);
        },
    });

    const createProcessMutation = useGraphQLMutation(WORKFLOW_INSTANCE_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Create process successfully']);
            navigate('/changeRequest');
        },
        onSettled: () => {
            setLoadingButton(null);
        },
    });

    // Mutation for creating comment with reason (only for TaskEdit)
    const createCommentMutation = useGraphQLMutation(TASK_COMMENT_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Reason saved successfully']);
            // Execute the pending terminate/reject action
            if (reasonActionType) {
                const data = getValues();
                onChangeSubmit(reasonActionType === 'terminate' ? ButtonType.TERMINATE : ButtonType.REJECT, data);
                setReasonActionType(null);
                setShowReasonModal(false);
                setLoadingButton(reasonActionType as ButtonType);
            }
        },
        onError: () => {
            setReasonActionType(null);
            setShowReasonModal(false);
            setLoadingButton(null);
        },
    });

    function extractButtonVariablesByType(data: any[], buttonType: ButtonType): Record<string, string> {
        return data?.reduce((acc, item) => {
            if (item?.type === 'button' && item?.buttonType === buttonType) {
                acc[item?.variableKey] = item?.variableValue;
            }
            return acc;
        }, {} as Record<string, string>);
    }

    const isValidValue = (value: any): boolean => {
        if (!value) return false;
        if (Array.isArray(value) && value.length === 0) return false;
        if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0) return false;
        return true;
    };

    // Helper functions for date range formatting
    const formatDateToMMDDYYYY = (date: Date): string => {
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${month}/${day}/${year}`;
    };

    const formatDateRangeValue = (value: [Date, Date] | null): string => {
        if (!value || !Array.isArray(value)) return '';
        const [startDate, endDate] = value;

        // Allow partial formatting - if only one date is valid, still format it
        if (startDate && isDateValid(startDate) && endDate && isDateValid(endDate)) {
            const startFormatted = formatDateToMMDDYYYY(startDate);
            const endFormatted = formatDateToMMDDYYYY(endDate);
            return `${startFormatted}-${endFormatted}`;
        }

        return '';
    };

    const parseDateRangeValue = (value: string): [Date, Date] | null => {
        if (!value) return null;

        const parts = value.split('-');
        if (parts.length !== 2) return null;

        try {
            const startDate = new Date(parts[0].trim());
            const endDate = new Date(parts[1].trim());

            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                return null;
            }

            return [startDate, endDate];
        } catch {
            return null;
        }
    };

    // Helper functions for date range state management
    const updateDateRangeState = (
        fieldKey: string,
        startDate: Date | null,
        endDate: Date | null,
        onChange: (value: string) => void
    ) => {
        setDateRangeStates((prev) => ({
            ...prev,
            [fieldKey]: { startDate, endDate },
        }));

        // Only format and call onChange if both dates are valid
        if (startDate && endDate && isDateValid(startDate) && isDateValid(endDate)) {
            const formattedValue = formatDateRangeValue([startDate, endDate]);
            onChange(formattedValue);
        }
    };

    const getDateRangeState = (fieldKey: string) => dateRangeStates[fieldKey] || { startDate: null, endDate: null };

    const initializeDateRangeState = (fieldKey: string, value: string) => {
        if (value && !dateRangeStates[fieldKey]) {
            const parsedValue = parseDateRangeValue(value);
            if (parsedValue) {
                setDateRangeStates((prev) => ({
                    ...prev,
                    [fieldKey]: { startDate: parsedValue[0], endDate: parsedValue[1] },
                }));
            }
        }
    };

    // Function to scroll to first error field
    const scrollToFirstError = (errors: Record<string, string>) => {
        const firstErrorKey = Object.keys(errors)[0];
        if (!firstErrorKey) return;

        // Find the field in schema to get its id
        const errorField = schemaForm?.form_fields?.find((field) => field.key === firstErrorKey);
        if (!errorField) return;

        // Try to find wrapper element by data-id
        const wrapperElement = document.querySelector(`[data-id="${errorField.id}"]`);
        if (wrapperElement) {
            wrapperElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest',
            });
            return;
        }
        // Try to find the element by field id
        const element = document.getElementById(errorField.id);
        if (
            element &&
            (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT')
        ) {
            setTimeout(() => element.focus(), 300);
        }
    };

    const extractVariables = (data: Record<string, any>): Record<string, any> => {
        const result: Record<string, any> = {};

        const variableFields = schemaForm?.form_fields?.filter((field) => field.isVariable) || [];

        for (const field of variableFields) {
            const value = data[field.key];
            if (isValidValue(value)) {
                result[field.key] = value;
            }
        }

        return result;
    };

    // Helper functions to validate table fields
    const validateAimsSystemField = (value: any): boolean => {
        if (!Array.isArray(value)) return false;
        return value.every(
            (row: any) =>
                row &&
                row.document_no?.trim() &&
                row.project_name?.trim() &&
                row.folder_link?.trim() &&
                !!row.status &&
                row.status !== '[Status]'
        );
    };

    const validateESmartIsoSystemField = (value: any): boolean => {
        if (!Array.isArray(value)) return false;
        return value.every(
            (row: any) =>
                row &&
                row.document_no?.trim() &&
                row.document_title?.trim() &&
                row.remark?.trim() &&
                !!row.status &&
                row.status !== '[Status]'
        );
    };

    const validatePhaDatabaseField = (value: any): boolean => {
        if (!Array.isArray(value)) return false;
        return value.every(
            (row: any) => row && row.document_no?.trim() && row.document_title?.trim() && row.remark?.trim()
        );
    };

    const getVisibleFieldKeys = (): string[] =>
        schemaForm?.form_fields
            ?.filter((form) => {
                if (shouldAlwaysShow) return true;

                const actualTypeAppear = (() => {
                    const keyAppear = form.keyAppear;
                    if (!keyAppear) return '';
                    const target = schemaForm.form_fields.find((f) => f.key === keyAppear);
                    if (!target) return '';
                    return target.type;
                })();

                if (form.keyAppear && form.valueAppear) {
                    const watchValue = watch(form.keyAppear, actualTypeAppear === 'checkbox' ? [] : '');
                    if (actualTypeAppear === 'checkbox') {
                        return Array.isArray(watchValue) && watchValue.includes(form.valueAppear);
                    }
                    return watchValue === form.valueAppear;
                }

                return form.valueAppear === '' || !form.keyAppear;
            })
            .map((form) => form.key) || [];

    const isFieldConditionallyRequired = (field: any, data: any): boolean => {
        // For textarea fields with conditional required logic
        if (field.type === 'text_area' && field.keyRequired && field.valueRequired && field.valueRequired.length > 0) {
            const watchValue = data[field.keyRequired];

            // Find the target field to determine its type
            const targetField = schemaForm?.form_fields?.find((f) => f.key === field.keyRequired);
            if (!targetField) return false;

            if (targetField.type === 'checkbox') {
                // For checkbox, check if any of the required values are selected
                return (
                    Array.isArray(watchValue) &&
                    field.valueRequired.some((reqValue: string) => watchValue.includes(reqValue))
                );
            } else if (targetField.type === 'radio' || targetField.type === 'select') {
                // For radio/select, check if the selected value is in required values
                return field.valueRequired.includes(watchValue);
            }
        }
        return false;
    };

    const shouldShowRequiredAsterisk = (field: any): boolean => {
        if (field.type !== 'text_area') return field.isRequired || false;

        // For textarea with conditional required
        if (field.keyRequired && field.valueRequired && field.valueRequired.length > 0) {
            const isConditionallyRequired = isFieldConditionallyRequired(field, watch());

            // Show asterisk if conditionally required OR if isRequired=true
            return isConditionallyRequired || field.isRequired;
        }

        // Default to isRequired for textarea without conditional logic
        return field.isRequired || false;
    };

    // Helper function to check if textarea should show error border due to conditional required
    const shouldShowTextareaErrorBorder = (field: any): boolean => {
        if (field.type !== 'text_area') return false;

        // Check if field has Required key and Required value settings
        if (field.keyRequired && field.valueRequired && field.valueRequired.length > 0) {
            const isConditionallyRequired = isFieldConditionallyRequired(field, watch());
            const currentValue = watch(field.key);

            // Show error border if conditionally required but field is empty
            return isConditionallyRequired && (!currentValue || currentValue.toString().trim() === '');
        }

        return false;
    };

    const validateRequiredFields = (
        data: Record<string, any>,
        visibleFieldKeys: string[] = []
    ): Record<string, string> => {
        const errors: Record<string, string> = {};

        const allFields = schemaForm?.form_fields || [];
        const requiredFields: any[] = [];

        for (const field of allFields) {
            const isFirstAreaOwner = field?.isFirstAreaOwner;
            const camundaVariables = isFirstAreaOwner
                ? dataUserTaskDetail?.workflow_instance?.camunda_variables?.[isFirstAreaOwner]
                : null;

            const isAppearFirstAreaOwner =
                !!isFirstAreaOwner &&
                !!assignee &&
                ((typeof camundaVariables === 'string' && camundaVariables !== assignee) ||
                    (Array.isArray(camundaVariables) && !camundaVariables.includes(assignee)));

            const isVisible = shouldAlwaysShow || visibleFieldKeys.includes(field.key);

            let shouldValidate = false;

            if (field.type === 'text_area' && field.keyRequired) {
                if (field.valueRequired && field.valueRequired.length > 0) {
                    shouldValidate = isFieldConditionallyRequired(field, data) && !isAppearFirstAreaOwner && isVisible;
                } else {
                    shouldValidate = !!field.isRequired && !isAppearFirstAreaOwner && isVisible;
                }
            } else {
                shouldValidate = !!field.isRequired && !isAppearFirstAreaOwner && isVisible;
            }

            if (shouldValidate) {
                requiredFields.push(field);
            }
        }

        for (const field of requiredFields) {
            const value = data[field.key];
            let isValid = true;

            switch (field.type as InputType) {
                case 'text':
                case 'number':
                case 'date':
                case 'date_range':
                case 'time':
                case 'text_area':
                    isValid = value && value.toString().trim() !== '';
                    break;
                case 'checkbox':
                case 'radio':
                    isValid = Array.isArray(value) ? value.length > 0 : !!value;
                    break;
                case 'select':
                    isValid = !!value;
                    break;
                case 'area_of_implementation':
                    if (value && typeof value === 'object') {
                        isValid = Object.values(value).some(
                            (selections: any) => Array.isArray(selections) && selections.length > 0
                        );
                    } else {
                        isValid = false;
                    }
                    break;
                case 'select_gate_keepers':
                    if (value && typeof value === 'object') {
                        isValid = Object.values(value).some((zone: any) => {
                            if (zone && typeof zone === 'object') {
                                return Object.values(zone).some((roleValue: any) => !!roleValue);
                            }
                            return false;
                        });
                    } else {
                        isValid = false;
                    }
                    break;
                case 'file':
                    isValid = !!value && value !== '';
                    break;
                case 'aims_system':
                    if (Array.isArray(value)) {
                        isValid = value.every(
                            (row: any) =>
                                row &&
                                row.document_no?.trim() &&
                                row.project_name?.trim() &&
                                row.folder_link?.trim() &&
                                !!row.status &&
                                row.status !== '[Status]'
                        );
                    } else {
                        isValid = false;
                    }

                    break;
                case 'pha_database':
                    if (Array.isArray(value)) {
                        isValid = value.every(
                            (row: any) =>
                                row && row.document_no?.trim() && row.document_title?.trim() && row.remark?.trim()
                        );
                    } else {
                        isValid = false;
                    }
                    break;
                case 'e_smart_iso_system':
                    if (Array.isArray(value)) {
                        isValid = value.every(
                            (row: any) =>
                                row &&
                                row.document_no?.trim() &&
                                row.document_title?.trim() &&
                                row.remark?.trim() &&
                                !!row.status &&
                                row.status !== '[Status]'
                        );
                    } else {
                        isValid = false;
                    }
                    break;
                default:
                    isValid = !!value;
            }
            if (!isValid) {
                errors[field.key] = 'This field is required';
            }
        }
        return errors;
    };

    // get all file in form

    const fileKeys = schemaForm?.form_fields?.filter((form) => form.type === 'file')?.map((form) => form.key) || [];

    const allValuesFile = watch();

    const fileIds = fileKeys.flatMap((key) => {
        const value = allValuesFile[key];
        return Array.isArray(value) ? value.filter((v) => typeof v === 'string' && v.trim() !== '') : [];
    });

    const onChangeSubmit = (actionType: ButtonType, data: any) => {
        const buttonTypeValidate = [ButtonType.APPROVE, ButtonType.SUBMIT];
        if (buttonTypeValidate.includes(actionType)) {
            const visibleFieldKeys = getVisibleFieldKeys();
            const errors = validateRequiredFields(data, visibleFieldKeys);
            setValidationErrors(errors);

            if (Object.keys(errors).length > 0) {
                // Scroll to first error field
                scrollToFirstError(errors);
                return;
            }
        }

        //only call api save_draft and submit in page change request
        const gateKeeperIds = Object.values(data?.selected_gatekeeper || {}).flatMap((roleMapping) =>
            Object.entries(roleMapping || {}).map(([roleId, userId]) => ({ roleId, userId }))
        );

        const status = actionType === ButtonType.SAVE_DRAFT ? 'DRAFT' : 'IN_PROGRESS';
        if (id && (isChangeRequestEditRoute || isChangeRequestAddRoute)) {
            updateProcessMutation.mutate({
                id,
                body: {
                    name: {
                        ...(initialData || {}),
                        ...data,
                    }?.request_title,
                    form_data: data,
                    status,
                    updated_by: user?.id,
                    variables: extractVariables({
                        ...(initialData || {}),
                        ...data,
                    }),
                    file_ids: fileIds,
                },
            });
        } else if (isChangeRequestEditRoute || isChangeRequestAddRoute) {
            createProcessMutation.mutate({
                input: {
                    name: {
                        ...(initialData || {}),
                        ...data,
                    }?.request_title,
                    status,
                    form_data: data,
                    variables: extractVariables({
                        ...(initialData || {}),
                        ...data,
                    }),
                    file_ids: fileIds,
                },
            });
        }

        if (actionType === ButtonType.APPROVE && isTaskEditRoute) {
            const variableButton = extractButtonVariablesByType(schemaForm?.form_fields, ButtonType.APPROVE);
            const variableField = extractVariables({
                ...data,
            });
            completeTaskMutation.mutate({
                input: {
                    id,
                    form_data: data,
                    status: 'APPROVED',
                    variables: {
                        ...variableField,
                        ...variableButton,
                        gatekeeper_multi: gateKeeperIds.length > 0 ? gateKeeperIds : undefined,
                    },
                    file_ids: fileIds,
                },
            });
        }
        if (actionType === ButtonType.TERMINATE && isTaskEditRoute) {
            const variableButton = extractButtonVariablesByType(schemaForm?.form_fields, ButtonType.TERMINATE);
            const variableField = extractVariables({
                ...data,
            });
            completeTaskMutation.mutate({
                input: {
                    id,
                    form_data: data,
                    status: 'TERMINATED',
                    variables: {
                        ...variableField,
                        ...variableButton,
                        gatekeeper_multi: gateKeeperIds.length > 0 ? gateKeeperIds : undefined,
                    },
                    file_ids: fileIds,
                },
            });
        }
        if (actionType === ButtonType.REJECT && isTaskEditRoute) {
            const variableButton = extractButtonVariablesByType(schemaForm?.form_fields, ButtonType.REJECT);
            const variableField = extractVariables({
                ...data,
            });
            completeTaskMutation.mutate({
                input: {
                    id,
                    form_data: data,
                    status: 'REJECTED',
                    variables: {
                        ...variableField,
                        ...variableButton,
                        gatekeeper_multi: gateKeeperIds.length > 0 ? gateKeeperIds : undefined,
                    },
                    file_ids: fileIds,
                },
            });
        }

        if (actionType === ButtonType.SUBMIT && isTaskEditRoute) {
            const variableButton = extractButtonVariablesByType(schemaForm?.form_fields, ButtonType.SUBMIT);
            const variableField = extractVariables({
                ...data,
            });
            completeTaskMutation.mutate({
                input: {
                    id,
                    form_data: data,
                    status: 'SUBMITTED',
                    variables: {
                        ...variableField,
                        ...variableButton,
                        gatekeeper_multi: gateKeeperIds.length > 0 ? gateKeeperIds : undefined,
                    },
                    file_ids: fileIds,
                },
            });
        }
    };

    const onSubmit = async (data: any, event?: React.BaseSyntheticEvent) => {
        const nativeEvent = event?.nativeEvent as SubmitEvent;
        const submitter = nativeEvent?.submitter as HTMLButtonElement | null;
        const actionType = submitter?.value as ButtonType | undefined;

        const buttonTypeValidate = [ButtonType.APPROVE, ButtonType.SUBMIT];
        if (!!actionType && buttonTypeValidate.includes(actionType)) {
            const visibleFieldKeys = getVisibleFieldKeys();
            const errors = validateRequiredFields(data, visibleFieldKeys);
            setValidationErrors(errors);

            if (Object.keys(errors).length > 0) {
                setLoadingButton(null);
                // Scroll to first error field
                scrollToFirstError(errors);
                return;
            }
        }

        if (isTaskEditRoute && (actionType === ButtonType.TERMINATE || actionType === ButtonType.REJECT)) {
            setReasonActionType(actionType === ButtonType.TERMINATE ? 'terminate' : 'reject');
            setShowReasonModal(true);
            return;
        }

        actionTypeRef.current = actionType ?? null;
        setIsAlert(true);
    };

    const shouldFetchPSSR = useMemo(
        () => schemaForm?.form_fields?.some((item) => item.dataSource === 'pssr_list'),
        [schemaForm?.form_fields]
    );

    const { data: dataPSSR } = useGraphQLQuery<UserListAllQuery, UserQueryVariables>(
        [QUERY_KEY.USERS_LIST_ALL],
        USER_LIST_ALL,
        {
            search: '',
            sort: '',
            filters: ['userAreaRoles.role.type:=(2)'],
        },
        '',
        {
            enabled: shouldFetchPSSR,
            placeholderData: keepPreviousData,
        }
    );

    const dataPssrList: FormFieldsCheckboxOption[] = useMemo(() => {
        if (!dataPSSR?.users_list_all) return [];

        return dataPSSR?.users_list_all?.map((user) => ({
            id: user.id,
            value: user.id,
            label: user.full_name,
        }));
    }, [dataPSSR]);

    useEffect(() => {
        const currentKeys = schemaForm?.form_fields?.map((f) => f.key);
        Object.keys(getValues()).forEach((key) => {
            if (!currentKeys?.includes(key)) {
                unregister(key);
            }
        });

        // Set default value cho các field select có defaultData
        schemaForm?.form_fields?.forEach((form) => {
            if (form.type === 'select' && form.defaultData !== undefined && form.defaultData !== '') {
                setValue(form.defaultData, dataUserTaskDetail?.workflow_instance?.camunda_variables[form.defaultData]);
            }
        });
    }, [schemaForm?.form_fields, dataPSSR, dataPssrList]);

    useEffect(() => {
        if (initialData) {
            reset(initialData);
        }
    }, [reset, initialData, listDataForm]);

    useEffect(() => {
        const subscription = watch((_, { name }) => {
            if (name) {
                // Find all fields with type 'select_gate_keepers'
                const selectGateKeepersFields =
                    schemaForm?.form_fields
                        ?.filter((field) => field.type === 'select_gate_keepers')
                        ?.map((field) => field.key) || [];

                // Check if the changed field is a nested field of any select_gate_keepers field
                const parentSelectGateKeepersField = selectGateKeepersFields.find((fieldKey) =>
                    name.startsWith(`${fieldKey}.`)
                );

                if (validationErrors[name]) {
                    // Default behavior for other field types
                    setValidationErrors((prev) => {
                        const newErrors = { ...prev };
                        delete newErrors[name];
                        return newErrors;
                    });
                }

                // Clear select_gate_keepers error when any nested field changes
                if (parentSelectGateKeepersField && validationErrors[parentSelectGateKeepersField]) {
                    setValidationErrors((prev) => {
                        const newErrors = { ...prev };
                        delete newErrors[parentSelectGateKeepersField];
                        return newErrors;
                    });
                }
            }
        });
        return () => subscription.unsubscribe();
    }, [watch, validationErrors, schemaForm?.form_fields]);

    const onSelectFields = (e: any, fields: FormFieldsType) => {
        if (shouldAlwaysShow) {
            e.stopPropagation();
            setActiveFields(activeFields?.id === fields.id ? null : fields);
            setListOption(fields?.option || []);
            setActiveOption(null);
        }
    };

    useLayoutEffect(() => {
        setActiveFields(null);
        setActiveOption(null);
    }, []);

    const getPreviousFormId = dataUserTaskDetail?.form?.schema?.settings?.previousForm || [];

    const filteredFormData = (dataUserTaskDetail?.workflow_instance?.user_tasks ?? []).filter(
        (item) =>
            item.workflow_step_id === dataUserTaskDetail?.workflow_step_id && getPreviousFormId.includes(item.form_id)
    );

    const filteredFormDataApproveSub = (dataUserTaskDetail?.workflow_instance?.user_tasks ?? []).filter(
        (item) =>
            item.workflow_step_id === dataUserTaskDetail?.workflow_step_id &&
            dataUserTaskDetail.task_key.includes(item.task_key)
    );

    const filteredFormDataApproveSubFormSubmit = (dataUserTaskDetail?.workflow_instance?.user_tasks ?? []).filter(
        (item) =>
            item.workflow_step_id === dataUserTaskDetail?.workflow_step_id && getPreviousFormId.includes(item.form_id)
    );

    const filteredFormDataApprove = (dataUserTaskDetail?.workflow_instance?.user_tasks ?? []).filter(
        (item) =>
            item.workflow_step_id === dataUserTaskDetail?.workflow_step_id &&
            dataUserTaskDetail.task_key.includes(item.task_key)
    );

    function getFormTasksWithOrder(tasks: UserTaskEntity[]): UserTaskEntity[] {
        const groupedByFormId: Record<string, UserTaskEntity[]> = {};

        tasks?.forEach((item) => {
            const formId = item?.form_id;
            if (!formId) return;
            if (!groupedByFormId[formId]) {
                groupedByFormId[formId] = [];
            }
            groupedByFormId[formId].push(item);
        });

        const result: UserTaskEntity[] = [];

        Object.entries(groupedByFormId).forEach(([formId, taskGroup]) => {
            const tasksWithOrder = taskGroup.filter((t) => (t?.order ?? 0) === dataUserTaskDetail?.order);
            result.push(...tasksWithOrder);
        });

        return result;
    }

    function getFormTasksWithOrderPreForm(tasks: UserTaskEntity[]): UserTaskEntity[] {
        const groupedByFormId: Record<string, UserTaskEntity[]> = {};

        tasks?.forEach((item) => {
            const formId = item?.form_id;
            if (!formId) return;
            if (!groupedByFormId[formId]) {
                groupedByFormId[formId] = [];
            }
            groupedByFormId[formId].push(item);
        });

        const result: UserTaskEntity[] = [];

        Object.entries(groupedByFormId).forEach(([formId, taskGroup]) => {
            const matchedOrderTasks = taskGroup.filter((t) => (t?.order ?? 0) === (dataUserTaskDetail?.order ?? 0));

            if (matchedOrderTasks.length > 0) {
                result.push(...matchedOrderTasks);
            } else {
                const fallbackTasks = taskGroup.filter((t) => (t?.order ?? 0) === 1);
                result.push(...fallbackTasks);
            }
        });

        return result;
    }

    const formDataPre = getFormTasksWithOrderPreForm(filteredFormData);
    const formDataApproveSub = getFormTasksWithOrder(filteredFormDataApproveSub);
    const formDataApproveSubFromSubmit = getFormTasksWithOrder(filteredFormDataApproveSubFormSubmit);
    const formDataApprove = getFormTasksWithOrder(filteredFormDataApprove);

    const shouldAlwaysShow = (isEditFormRoute || isFormAddRoute) && !isPreview;

    // Helper function to get field colors
    const getFieldColors = (form: any) => ({
        labelColor: form.labelColor || schemaForm?.settings?.fieldLabelColor || '#5e5873',
        backgroundColor: form.backgroundColor || schemaForm?.settings?.formBackgroundColor || 'transparent',
    });

    const isStatusFormCurrent = dataUserTaskDetail?.form?.schema?.form_fields?.some(
        (item) => item?.buttonType === ButtonType.SUBMIT
    );
    const handleClickOutside = () => {
        setActiveFields(null);
        setActiveOption(null);
    };

    useClickOutside(wrapperRef, handleClickOutside, ['not_click_outside']);

    const handleReasonSubmit = (reason: string) => {
        if (reasonActionType && id) {
            createCommentMutation.mutate({
                user_task_id: id,
                content: `${reasonActionType === 'terminate' ? 'Terminate' : 'Reject'} reason: ${reason}`,
            });
        }
    };

    const handleReasonCancel = () => {
        setShowReasonModal(false);
        setReasonActionType(null);
    };
    return (
        <>
            {(isTaskEditRoute || isTaskViewRoute) &&
                formDataPre?.length > 0 &&
                !dataUserTaskDetail?.subprocess_element_value &&
                !dataUserTaskDetail?.subprocess_element_variable &&
                formDataPre.map((data, index) => (
                    <PreviousFormFields
                        key={index}
                        initialData={data?.form_data}
                        schemaForm={data?.form?.schema as FormItem}
                        dataPlanAndAreaUnit={dataPlanAndAreaUnit}
                        assignee={data?.assignee}
                        firstAreaOwner={dataUserTaskDetail?.workflow_instance?.camunda_variables?.firstAreaOwner}
                        createBy={data.assigneeInfo.full_name}
                        status={data.status}
                        stepName={data?.task_name}
                        isFileEntry
                    />
                ))}

            {(isTaskEditRoute || isTaskViewRoute) &&
                formDataPre?.length > 0 &&
                dataUserTaskDetail?.subprocess_element_value &&
                dataUserTaskDetail?.subprocess_element_variable &&
                !isStatusFormCurrent &&
                formDataPre
                    .filter((item) => item.subprocess_element_value === dataUserTaskDetail.subprocess_element_value)
                    .map(
                        (data, index) =>
                            data?.form?.schema?.form_fields?.some(
                                (item) => item?.buttonType !== ButtonType.APPROVE
                            ) && (
                                <PreviousFormFields
                                    key={index}
                                    initialData={data?.form_data}
                                    status={data.status}
                                    createBy={data.assigneeInfo.full_name}
                                    schemaForm={data?.form?.schema as FormItem}
                                    dataPlanAndAreaUnit={dataPlanAndAreaUnit}
                                    assignee={data?.assignee}
                                    firstAreaOwner={
                                        dataUserTaskDetail?.workflow_instance?.camunda_variables?.firstAreaOwner
                                    }
                                    stepName={data?.task_name}
                                    isFileEntry
                                />
                            )
                    )}

            {(isTaskEditRoute || isTaskViewRoute) &&
                formDataApproveSub?.length > 0 &&
                dataUserTaskDetail?.subprocess_element_value &&
                dataUserTaskDetail?.subprocess_element_variable &&
                !isStatusFormCurrent && (
                    <div
                        className={classNames(
                            'card tw-p-8 tw-max-w-[1000px] tw-mx-auto border tw-mb-8  tw-bg-[whitesmoke]'
                        )}
                    >
                        <div>
                            <p className="tw-font-bold tw-mb-2  tw-max-w-[1000px]">Review Result</p>
                            <div className="tw-overflow-auto  tw-max-w-[1000px] tw-bg-white">
                                <table className="tw-w-full tw-border tw-border-collapse">
                                    <thead className="tw-bg-gray-100">
                                        <tr>
                                            <th className="tw-border tw-p-2 tw-text-center tw-w-[5%]">No</th>
                                            <th className="tw-border tw-p-2 tw-w-[15%]">Approval Role</th>
                                            <th className="tw-border tw-p-2 tw-w-[15%]">Zone Name</th>
                                            <th className="tw-border tw-p-2 tw-w-[15%]">Reviewer Name</th>
                                            <th className="tw-border tw-p-2 tw-w-[15%]">Updated On</th>
                                            <th className="tw-border tw-p-2 tw-text-center tw-w-[10%]">Decision</th>
                                            <th className="tw-border tw-p-2 tw-w-[25%]">Remark</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {formDataApproveSub?.reduce((rows, data, idx) => {
                                            const formFields = data?.form?.schema?.form_fields || [];
                                            const hasApproveButton = formFields.some(
                                                (item) => item?.buttonType === ButtonType.APPROVE
                                            );
                                            if (!hasApproveButton) return rows;

                                            const getKeyRemark = formFields.find((item) => item?.isRemark)?.key;

                                            const remarkValue = getKeyRemark ? data?.form_data?.[getKeyRemark] : '';

                                            const rowIndex = rows.length + 1;

                                            rows.push(
                                                <tr key={idx}>
                                                    <td className="tw-border tw-p-2 tw-text-center">{rowIndex}</td>
                                                    <td className="tw-border tw-p-2">
                                                        {data?.userRoles?.map((item) => item.name).join(', ')}
                                                    </td>
                                                    <td className="tw-border tw-p-2">
                                                        {data?.user_zones?.map((item) => item.name).join(', ')}
                                                    </td>
                                                    <td className="tw-border tw-p-2">
                                                        {data?.assigneeInfo?.full_name}
                                                    </td>
                                                    <td className="tw-border tw-p-2">
                                                        {data?.status !== TaskStatus.IN_PROGRESS &&
                                                            formatDateTime(
                                                                data?.updated_at,
                                                                FORMAT_DATE.SHOW_DATE_MINUTE
                                                            )}
                                                    </td>
                                                    <td className="tw-border tw-p-2 tw-text-center">
                                                        {getStatusBadgeStep(data?.status)}
                                                    </td>
                                                    <td className="tw-border tw-p-2">{remarkValue}</td>
                                                </tr>
                                            );

                                            return rows;
                                        }, [] as JSX.Element[])}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                )}
            {(isTaskEditRoute || isTaskViewRoute) &&
                formDataApproveSubFromSubmit?.length > 0 &&
                dataUserTaskDetail?.subprocess_element_value &&
                dataUserTaskDetail?.subprocess_element_variable &&
                isStatusFormCurrent && (
                    <>
                        <div
                            className={classNames(
                                'card tw-p-8 tw-max-w-[1000px] tw-mx-auto border tw-mb-8  tw-bg-[whitesmoke]'
                            )}
                        >
                            <div>
                                <p className="tw-font-bold tw-mb-2 tw-max-w-[1000px]">Review Result</p>
                                <div className="tw-overflow-auto tw-max-w-[1000px] tw-bg-white">
                                    <table className="tw-w-full tw-border tw-border-collapse">
                                        <thead className="tw-bg-gray-100">
                                            <tr>
                                                <th className="tw-border tw-p-2 tw-text-center tw-w-[5%]">No</th>
                                                <th className="tw-border tw-p-2 tw-w-[15%]">Approval Role</th>
                                                <th className="tw-border tw-p-2 tw-w-[15%]">Zone Name</th>
                                                <th className="tw-border tw-p-2 tw-w-[15%]">Reviewer Name</th>
                                                <th className="tw-border tw-p-2 tw-w-[15%]">Updated On</th>
                                                <th className="tw-border tw-p-2 tw-text-center tw-w-[10%]">Decision</th>
                                                <th className="tw-border tw-p-2 tw-w-[25%]">Remark</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {formDataApproveSubFromSubmit
                                                .filter(
                                                    (item) =>
                                                        item.subprocess_element_value ===
                                                        dataUserTaskDetail.subprocess_element_value
                                                )
                                                .reduce((rows, data, idx) => {
                                                    const formFields = data?.form?.schema?.form_fields || [];
                                                    const hasApproveButton = formFields.some(
                                                        (item) => item?.buttonType === ButtonType.APPROVE
                                                    );
                                                    if (!hasApproveButton) return rows;

                                                    const getKeyRemark = formFields.find((item) => item?.isRemark)?.key;

                                                    const remarkValue = getKeyRemark
                                                        ? data?.form_data?.[getKeyRemark]
                                                        : '';

                                                    const rowIndex = rows.length + 1;

                                                    rows.push(
                                                        <tr key={idx}>
                                                            <td className="tw-border tw-p-2 tw-text-center">
                                                                {rowIndex}
                                                            </td>
                                                            <td className="tw-border tw-p-2">
                                                                {data?.userRoles?.map((item) => item.name).join(', ')}
                                                            </td>
                                                            <td className="tw-border tw-p-2">
                                                                {data?.user_zones?.map((item) => item.name).join(', ')}
                                                            </td>
                                                            <td className="tw-border tw-p-2">
                                                                {data?.assigneeInfo?.full_name}
                                                            </td>
                                                            <td className="tw-border tw-p-2">
                                                                {data?.status !== TaskStatus.IN_PROGRESS &&
                                                                    formatDateTime(
                                                                        data?.updated_at,
                                                                        FORMAT_DATE.SHOW_DATE_MINUTE
                                                                    )}
                                                            </td>
                                                            <td className="tw-border tw-p-2 tw-text-center">
                                                                {getStatusBadgeStep(data?.status)}
                                                            </td>
                                                            <td className="tw-border tw-p-2">{remarkValue}</td>
                                                        </tr>
                                                    );

                                                    return rows;
                                                }, [] as JSX.Element[])}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </>
                )}

            {(isTaskEditRoute || isTaskViewRoute) &&
                formDataApprove?.length > 0 &&
                dataUserTaskDetail?.element_value &&
                dataUserTaskDetail?.element_variable && <TableReview data={formDataApprove} />}

            <FormProvider {...methods}>
                <div
                    ref={wrapperRef}
                    className={classNames('card tw-mb-0 tw-p-6 tw-max-w-[1000px] tw-mx-auto not_click_outside', {
                        border: !isEditFormRoute && !isFormAddRoute,
                        'tw-pointer-events-none': !!loadingButton,
                    })}
                    style={{
                        backgroundColor: schemaForm?.settings?.formBackgroundColor || '#ffffff',
                    }}
                >
                    <div className="content-body">
                        {schemaForm?.settings?.title && (
                            <h2
                                className="card-title tw-mx-2 tw-py-2 tw-border-b tw-border-b-[#e6e6e8] !tw-font-bold"
                                style={{
                                    color: schemaForm?.settings?.formTitleColor || '#5e5873',
                                }}
                            >
                                {schemaForm?.settings?.title}
                            </h2>
                        )}
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <div
                                className="tw-flex tw-flex-wrap"
                                style={{
                                    justifyContent: schemaForm?.settings?.formAlignment,
                                }}
                            >
                                {schemaForm?.form_fields?.map((form, index) => {
                                    const findTypeAppearFromKey = (
                                        keyAppear: string
                                    ): 'checkbox' | 'radio' | 'select' | '' => {
                                        if (!keyAppear || !schemaForm?.form_fields) return '';
                                        const targetField = schemaForm.form_fields.find(
                                            (field) => field.key === keyAppear
                                        );
                                        if (!targetField) return '';
                                        switch (targetField.type) {
                                            case 'checkbox':
                                                return 'checkbox';
                                            case 'radio':
                                                return 'radio';
                                            case 'select':
                                                return 'select';
                                            default:
                                                return '';
                                        }
                                    };

                                    const actualTypeAppear = form.keyAppear && findTypeAppearFromKey(form.keyAppear);

                                    const isVisible =
                                        form.keyAppear && form.valueAppear
                                            ? actualTypeAppear === 'checkbox'
                                                ? watch(form.keyAppear || '', [])?.includes?.(form?.valueAppear)
                                                : watch(form.keyAppear || '') === form?.valueAppear
                                            : form.valueAppear === '' || !form.keyAppear;

                                    const isFirstAreaOwner = form?.isFirstAreaOwner;

                                    const camundaVariables =
                                        isFirstAreaOwner &&
                                        dataUserTaskDetail?.workflow_instance?.camunda_variables?.[isFirstAreaOwner];

                                    const isAppearFirstAreaOwner =
                                        isFirstAreaOwner &&
                                        assignee &&
                                        ((typeof camundaVariables === 'string' && camundaVariables !== assignee) ||
                                            (Array.isArray(camundaVariables) && !camundaVariables.includes(assignee)));

                                    if (form.type === 'text' || form.type === 'number' || form.type === 'time') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        alignItems: form.isTable ? 'center' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames({
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <input
                                                        className={classNames('form-control', {
                                                            'tw-pointer-events-none': shouldAlwaysShow,
                                                        })}
                                                        style={{
                                                            height: `${form.height}px`,
                                                            background: 'transparent',
                                                        }}
                                                        id={form.id}
                                                        type={form.type}
                                                        placeholder={form.placeholder}
                                                        {...register(form.key)}
                                                        {...(((!isEditFormRoute && !isFormAddRoute) || isPreview) && {
                                                            disabled: isDisableForm,
                                                        })}
                                                    />
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'date') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        alignItems: form.isTable ? 'center' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames({
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <Controller
                                                        name={form.key}
                                                        control={control}
                                                        defaultValue={null}
                                                        render={({ field }) => (
                                                            <DatePicker
                                                                placeholderText="dd/mm/yyyy"
                                                                dateFormat="dd/MM/yyyy"
                                                                onChange={(date) => field.onChange(date)}
                                                                selected={field.value}
                                                                wrapperClassName={`col-12`}
                                                                id={form.id}
                                                                className={classNames(
                                                                    `form-control !tw-bg-transparent`,
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                customInput={
                                                                    <input
                                                                        style={{
                                                                            height: `${form.height}px`,
                                                                            background: 'transparent',
                                                                        }}
                                                                        id={form.id}
                                                                        {...(((!isEditFormRoute && !isFormAddRoute) ||
                                                                            isPreview) && {
                                                                            disabled: isDisableForm,
                                                                        })}
                                                                    />
                                                                }
                                                            />
                                                        )}
                                                    />
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }

                                    if (form.type === 'text_area') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        alignItems: form.isTable ? 'center' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames({
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {shouldShowRequiredAsterisk(form) && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <textarea
                                                        className={classNames('form-control', {
                                                            'tw-pointer-events-none': shouldAlwaysShow,
                                                            'tw-border-red-500': shouldShowTextareaErrorBorder(form),
                                                        })}
                                                        style={{
                                                            height: `${form.height}px`,
                                                            maxHeight: '180px',
                                                            background: 'transparent',
                                                            ...(shouldShowTextareaErrorBorder(form) && {
                                                                borderColor: '#dc3545',
                                                                borderWidth: '1px',
                                                            }),
                                                        }}
                                                        id={form.id}
                                                        placeholder={form.placeholder}
                                                        {...register(form.key)}
                                                        {...(((!isEditFormRoute && !isFormAddRoute) || isPreview) && {
                                                            disabled: isDisableForm,
                                                        })}
                                                    />
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'date_range') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        alignItems: form.isTable ? 'center' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames({
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <Controller
                                                        name={form.key}
                                                        control={control}
                                                        render={({ field: { onChange, value } }) => {
                                                            // Initialize date range state from existing value
                                                            initializeDateRangeState(form.key, value || '');

                                                            const currentState = getDateRangeState(form.key);
                                                            const displayValue: [Date, Date] | null =
                                                                currentState.startDate && currentState.endDate
                                                                    ? [currentState.startDate, currentState.endDate]
                                                                    : parseDateRangeValue(value || '');

                                                            return (
                                                                <div
                                                                    className={classNames('custom-date-range-wrapper', {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    })}
                                                                    style={
                                                                        {
                                                                            height: `${form.height}px`,
                                                                            '--date-range-height': `${form.height}px`,
                                                                        } as React.CSSProperties
                                                                    }
                                                                >
                                                                    <RsuiteDateRangePicker
                                                                        format="dd/MM/yyyy"
                                                                        character=" – "
                                                                        className="form-control new-date-range-picker"
                                                                        onOk={(newValue) => {
                                                                            if (
                                                                                newValue &&
                                                                                Array.isArray(newValue) &&
                                                                                newValue.length === 2
                                                                            ) {
                                                                                updateDateRangeState(
                                                                                    form.key,
                                                                                    newValue[0],
                                                                                    newValue[1],
                                                                                    onChange
                                                                                );
                                                                            }
                                                                        }}
                                                                        onChange={(newValue) => {
                                                                            if (newValue && Array.isArray(newValue)) {
                                                                                const [startDate, endDate] = newValue;
                                                                                const currentState = getDateRangeState(
                                                                                    form.key
                                                                                );
                                                                                const newStartDate =
                                                                                    startDate && isDateValid(startDate)
                                                                                        ? startDate
                                                                                        : currentState.startDate;
                                                                                const newEndDate =
                                                                                    endDate && isDateValid(endDate)
                                                                                        ? endDate
                                                                                        : currentState.endDate;

                                                                                updateDateRangeState(
                                                                                    form.key,
                                                                                    newStartDate,
                                                                                    newEndDate,
                                                                                    onChange
                                                                                );
                                                                            }
                                                                        }}
                                                                        onClean={() => {
                                                                            setDateRangeStates((prev) => ({
                                                                                ...prev,
                                                                                [form.key]: {
                                                                                    startDate: null,
                                                                                    endDate: null,
                                                                                },
                                                                            }));
                                                                            onChange('');
                                                                        }}
                                                                        value={displayValue}
                                                                        disabled={
                                                                            shouldAlwaysShow ||
                                                                            (!isEditFormRoute && !isFormAddRoute) ||
                                                                            isPreview
                                                                                ? isDisableForm
                                                                                : false
                                                                        }
                                                                        placeholder="dd/mm/yyyy - dd/mm/yyyy"
                                                                    />
                                                                </div>
                                                            );
                                                        }}
                                                    />
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'checkbox') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    {(form.dataSource === 'custom' ? form?.option : dataPssrList)?.map(
                                                        (cb) => (
                                                            <div className="form-check tw-mb-[4px]" key={cb.id}>
                                                                <input
                                                                    className={classNames('form-check-input', {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    })}
                                                                    type="checkbox"
                                                                    id={cb.id}
                                                                    value={cb.value}
                                                                    {...register(form.key)}
                                                                    {...(((!isEditFormRoute && !isFormAddRoute) ||
                                                                        isPreview) && {
                                                                        disabled: isDisableForm,
                                                                    })}
                                                                />
                                                                <label
                                                                    className={classNames('form-check-label', {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    })}
                                                                    htmlFor={cb.id}
                                                                >
                                                                    {cb.label}
                                                                </label>
                                                            </div>
                                                        )
                                                    )}
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'radio') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',

                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}

                                                    {(form.dataSource === 'custom' ? form?.option : dataPssrList)?.map(
                                                        (cb) => (
                                                            <div
                                                                className={classNames('form-check tw-mb-[4px]', {
                                                                    'tw-pointer-events-none':
                                                                        (isEditFormRoute || isFormAddRoute) &&
                                                                        !isPreview,
                                                                })}
                                                                key={cb.id}
                                                            >
                                                                <input
                                                                    className={classNames('form-check-input', {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    })}
                                                                    type="radio"
                                                                    id={cb.id}
                                                                    value={cb.value}
                                                                    {...register(form.key)}
                                                                    {...(((!isEditFormRoute && !isFormAddRoute) ||
                                                                        isPreview) && {
                                                                        disabled: isDisableForm,
                                                                    })}
                                                                />
                                                                <label
                                                                    htmlFor={cb.id}
                                                                    className={classNames('form-check-label', {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    })}
                                                                >
                                                                    {cb.label}
                                                                </label>
                                                            </div>
                                                        )
                                                    )}
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'select') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        alignItems: form.isTable ? 'center' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                        outline: shouldAlwaysShow
                                                            ? activeFields?.id === form.id
                                                                ? '1px solid #00aff0'
                                                                : ''
                                                            : validationErrors[form.key] && form.isTable
                                                            ? '1px solid #dc3545'
                                                            : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <select
                                                        className={classNames('form-select', {
                                                            'tw-pointer-events-none': shouldAlwaysShow,
                                                        })}
                                                        style={{ height: `${form.height}px` }}
                                                        key={form.id}
                                                        id={form.id}
                                                        {...(((!isEditFormRoute && !isFormAddRoute) || isPreview) && {
                                                            disabled: isDisableForm,
                                                        })}
                                                        {...register(form.key)}
                                                    >
                                                        <option value="">-- Please select --</option>
                                                        {form.dataSource === 'custom'
                                                            ? form?.option?.map((opt) => (
                                                                  <option key={opt.id} value={opt.value}>
                                                                      {opt.label}
                                                                  </option>
                                                              ))
                                                            : dataPssrList?.map((opt) => (
                                                                  <option key={opt.id} value={opt.value}>
                                                                      {opt.label}
                                                                  </option>
                                                              ))}
                                                    </select>
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'free_text') {
                                        return (
                                            (shouldAlwaysShow || isVisible) &&
                                            !isAppearFirstAreaOwner && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${
                                                            form.isTable
                                                                ? `calc(${form.width}% - 0.5px)`
                                                                : `${form.width}%`
                                                        }`,
                                                        height: 'auto',
                                                        paddingInline: '6px',
                                                        paddingBlock: '4px',
                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable ? '#f3f2f7' : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col ql-editor', {
                                                        'tw-hidden': isAppearFirstAreaOwner,
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                    dangerouslySetInnerHTML={{ __html: form.label || '' }}
                                                ></div>
                                            )
                                        );
                                    }
                                    if (form.type === 'area_of_implementation') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${form.width}%`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        background: getFieldColors(form).backgroundColor,
                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <PlanAndAreaUnit
                                                        control={control}
                                                        name={form.key}
                                                        disabled={
                                                            isPreview
                                                                ? isDisableForm
                                                                : isEditFormRoute ||
                                                                  isFormAddRoute ||
                                                                  isWorkflowInstanceRoute ||
                                                                  isChangeRequestViewRoute
                                                        }
                                                    />
                                                    {validationErrors[form.key] && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }

                                    if (form.type === 'select_gate_keepers') {
                                        return (
                                            <React.Fragment key={index}>
                                                {formDataApprove?.length > 0 &&
                                                    formDataApprove.filter(
                                                        (item) =>
                                                            item.status === TaskStatus.APPROVED ||
                                                            item.status === TaskStatus.SUBMITTED ||
                                                            item.status === TaskStatus.REJECTED
                                                    ).length > 0 && (
                                                        <div
                                                            style={{
                                                                width: `${form.width}%`,
                                                                paddingInline: '6px',
                                                                paddingBlock: '10px',
                                                                background: getFieldColors(form).backgroundColor,
                                                            }}
                                                        >
                                                            <SelectGateKeepersReviewWrapper
                                                                formDataApprove={formDataApprove}
                                                                dataPlanAndAreaUnit={dataPlanAndAreaUnit}
                                                                name={form.key}
                                                            />
                                                        </div>
                                                    )}
                                                {(shouldAlwaysShow || isVisible) && (
                                                    <div
                                                        data-id={form.id}
                                                        onClick={(e) => {
                                                            onSelectFields(e, form);
                                                        }}
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            background: getFieldColors(form).backgroundColor,
                                                            outline: shouldAlwaysShow
                                                                ? activeFields?.id === form.id
                                                                    ? '1px solid #00aff0'
                                                                    : ''
                                                                : validationErrors[form.key] && form.isTable
                                                                ? '1px solid #dc3545'
                                                                : '',
                                                        }}
                                                        className={classNames('tw-flex tw-flex-col', {
                                                            'tw-cursor-pointer': shouldAlwaysShow,
                                                            'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                                shouldAlwaysShow,
                                                        })}
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className={classNames(
                                                                        'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                        {
                                                                            'tw-pointer-events-none': shouldAlwaysShow,
                                                                        }
                                                                    )}
                                                                    htmlFor={form.id}
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        {!shouldAlwaysShow && (
                                                                            <div
                                                                                className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                                dangerouslySetInnerHTML={{
                                                                                    __html: form.tooltip || '',
                                                                                }}
                                                                            ></div>
                                                                        )}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <SelectGateKeepers
                                                            key={index}
                                                            control={control}
                                                            name={form.key}
                                                            disabled={isDisableForm || isTaskViewRoute || false}
                                                            dataPlanAndAreaUnit={dataPlanAndAreaUnit}
                                                            userZones={dataUserTaskDetail?.user_zones}
                                                        />
                                                        {validationErrors[form.key] && !form.isTable && (
                                                            <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                                {validationErrors[form.key]}
                                                            </small>
                                                        )}
                                                    </div>
                                                )}
                                            </React.Fragment>
                                        );
                                    }

                                    if (form.type === 'aims_system') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${form.width}%`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        background: getFieldColors(form).backgroundColor,
                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <AimsSystem
                                                        control={control}
                                                        name={form.key}
                                                        isPreview={isPreview}
                                                        disabled={isDisableForm || isTaskViewRoute || false}
                                                    />{' '}
                                                    {validationErrors[form.key] && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'e_smart_iso_system') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${form.width}%`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        background: getFieldColors(form).backgroundColor,
                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <ESmartIsoSystem
                                                        control={control}
                                                        name={form.key}
                                                        isPreview={isPreview}
                                                        disabled={isDisableForm || isTaskViewRoute || false}
                                                    />
                                                    {validationErrors[form.key] && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'pha_database') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${form.width}%`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        background: getFieldColors(form).backgroundColor,
                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>
                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <PhaDatabase
                                                        control={control}
                                                        name={form.key}
                                                        isPreview={isPreview}
                                                        disabled={isDisableForm || isTaskViewRoute || false}
                                                    />{' '}
                                                    {validationErrors[form.key] && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'file') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    key={index}
                                                    style={{
                                                        width: `${form.width}%`,
                                                        paddingInline: '6px',
                                                        paddingBlock: '10px',
                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                        margin: form.isTable ? '0.5px' : '',
                                                        display: form.isTable ? 'flex' : '',
                                                        alignItems: form.isTable ? 'center' : '',
                                                        justifyContent: form.isTable ? 'center' : '',
                                                        background: form.isTable
                                                            ? '#f3f2f7'
                                                            : getFieldColors(form).backgroundColor,
                                                    }}
                                                    className={classNames('tw-flex tw-flex-col', {
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    {form.label && !form.isTable && (
                                                        <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                            <label
                                                                className={classNames(
                                                                    'form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]',
                                                                    {
                                                                        'tw-pointer-events-none': shouldAlwaysShow,
                                                                    }
                                                                )}
                                                                htmlFor={form.id}
                                                                style={{
                                                                    color: getFieldColors(form).labelColor,
                                                                }}
                                                            >
                                                                {form.label}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                            {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                    >
                                                                        <AlertCircle size={14} />
                                                                    </button>

                                                                    {!shouldAlwaysShow && (
                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <FileUpload
                                                        name={form.key}
                                                        disabled={
                                                            isDisableForm ||
                                                            isEditFormRoute ||
                                                            isFormAddRoute ||
                                                            !!loadingButton
                                                        }
                                                    />
                                                    {validationErrors[form.key] && !form.isTable && (
                                                        <small className="tw-text-red-500 tw-text-xs tw-mt-1 tw-block">
                                                            {validationErrors[form.key]}
                                                        </small>
                                                    )}
                                                </div>
                                            )
                                        );
                                    }
                                    if (form.type === 'tooltip') {
                                        return (
                                            (shouldAlwaysShow || isVisible) && (
                                                <div
                                                    key={index}
                                                    data-id={form.id}
                                                    onClick={(e) => {
                                                        onSelectFields(e, form);
                                                    }}
                                                    style={{
                                                        width: `${form.width}%`,

                                                        outline:
                                                            activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                    }}
                                                    className={classNames('tw-relative tw-flex tw-items-center', {
                                                        'tw-cursor-pointer': shouldAlwaysShow,
                                                        'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                            shouldAlwaysShow,
                                                    })}
                                                >
                                                    <div className="tw-relative tw-inline-block tw-group">
                                                        <button
                                                            type="button"
                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                        >
                                                            <AlertCircle size={14} />
                                                        </button>
                                                        {!shouldAlwaysShow && (
                                                            <div
                                                                className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                dangerouslySetInnerHTML={{
                                                                    __html: form.label || '',
                                                                }}
                                                            ></div>
                                                        )}
                                                    </div>
                                                </div>
                                            )
                                        );
                                    }

                                    if (form.type === 'button') {
                                        return (
                                            <div
                                                onClick={(e) => {
                                                    onSelectFields(e, form);
                                                }}
                                                key={index}
                                                style={{
                                                    width: `${form.width}%`,
                                                    paddingInline: '6px',
                                                    paddingBlock: '10px',
                                                    display: isDisableForm ? 'none' : 'auto',
                                                    outline: activeFields?.id === form.id ? '1px solid #00aff0' : '',
                                                }}
                                                className={classNames('tw-flex tw-flex-col', {
                                                    'tw-cursor-pointer': shouldAlwaysShow,
                                                    'hover:tw-outline hover:tw-outline-1 hover:tw-outline-[#00aff0]':
                                                        shouldAlwaysShow,
                                                })}
                                            >
                                                <button
                                                    key={form.id}
                                                    value={form.buttonType}
                                                    type="submit"
                                                    disabled={!!loadingButton}
                                                    className={classNames(
                                                        'btn btn-primary waves-effect waves-light tw-min-h-[38px]',
                                                        {
                                                            // 'tw-pointer-events-none': isEditFormRoute || isFormAddRoute,
                                                        }
                                                    )}
                                                >
                                                    {loadingButton === form.buttonType ? 'Loading...' : form.label}
                                                </button>
                                            </div>
                                        );
                                    }
                                })}
                            </div>
                        </form>
                    </div>
                </div>
                <ModalConfirm
                    show={isAlert}
                    text={COMMON_MESSAGE.ACTION_CONFIRM}
                    btnDisabled={false}
                    textTitle="Confirm"
                    changeShow={(s: boolean) => {
                        setIsAlert(s), setLoadingButton(null);
                    }}
                    submitAction={() => {
                        const data = getValues();
                        const actionType = actionTypeRef.current as ButtonType;
                        const buttonTypeLoading = [ButtonType.APPROVE, ButtonType.SUBMIT, ButtonType.SAVE_DRAFT];
                        if (buttonTypeLoading.includes(actionType)) {
                            setLoadingButton(actionType);
                        }

                        switch (actionType) {
                            case 'cancel':
                                navigate(-1);
                                break;

                            case 'approve':
                                onChangeSubmit(ButtonType.APPROVE, data);
                                break;

                            case 'terminate':
                                onChangeSubmit(ButtonType.TERMINATE, data);
                                break;

                            case 'reject':
                                onChangeSubmit(ButtonType.REJECT, data);
                                break;

                            case 'save_draft':
                                onChangeSubmit(ButtonType.SAVE_DRAFT, data);
                                break;
                            case 'submit':
                                onChangeSubmit(ButtonType.SUBMIT, data);
                                break;

                            default:
                                console.warn('No action selected');
                        }

                        actionTypeRef.current = null;
                        setIsAlert(false);
                    }}
                />
                {showReasonModal && reasonActionType && (
                    <ModalReasonConfirm
                        show={showReasonModal}
                        text={`Are you sure you want to ${reasonActionType} this task? Please provide a reason.`}
                        textTitle={`${reasonActionType === 'terminate' ? 'Terminate' : 'Reject'} Task`}
                        actionType={reasonActionType}
                        btnDisabled={createCommentMutation.isPending}
                        isLoading={createCommentMutation.isPending}
                        changeShow={handleReasonCancel}
                        submitAction={handleReasonSubmit}
                    />
                )}
            </FormProvider>
        </>
    );
}
