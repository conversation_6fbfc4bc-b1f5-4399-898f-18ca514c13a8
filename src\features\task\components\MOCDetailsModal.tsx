import React, { useState } from 'react';
import classNames from 'classnames';
import { getOrdinalSuffix, isValidFormApproval, toggleModalOpen } from 'utils/common';
import CollapsibleForm from './CollapsibleForm';
import TaskCommentsSection from './TaskCommentsSection';
import TaskAttachmentsSection from './TaskAttachmentsSection';
import TaskComment, { ITaskCommentGroup } from '../../../types/TaskComment';
import { Task, TaskFile, TaskStatus } from '../../../types/Task';
import { IFile } from '../../../types/common/Item';
import PreviousFormFields from 'features/form/components/PreviousFormFields';
import { FormItem } from 'types/Form';
import { UserTaskEntity } from 'types/Workflow';
import { TableReview } from 'components/partials/TableReview';

interface FileAttachment {
    workflow_step_id: string;
    workflow_step_name: string;
    workflow_step_order: number;
    files: (TaskFile | IFile)[];
}

interface IProps {
    show: boolean;
    changeShow: (show: boolean) => void;
    taskComments: TaskComment[];
    activeTab?: string;
    onSendComment?: (content: string) => void;
    onDeleteComment?: (id: string) => void;
    isSendingComment?: boolean;
    userTaskDetail?: Task;
    groupedTaskComments: ITaskCommentGroup[];
    fileAttachments: FileAttachment[];
    fileLength: number;
}

export default function MOCDetailsModal({
    show,
    changeShow,
    taskComments,
    activeTab = 'moc-details',
    onSendComment,
    onDeleteComment,
    isSendingComment,
    userTaskDetail,
    groupedTaskComments,
    fileAttachments,
    fileLength,
}: Readonly<IProps>) {
    const [currentTab, setCurrentTab] = useState(activeTab);

    React.useLayoutEffect(() => toggleModalOpen(show), [show]);
    React.useEffect(() => {
        setCurrentTab(activeTab);
    }, [activeTab]);

    const handleTabClick = (tabId: string) => {
        setCurrentTab(tabId);
    };

    const isFirstForm = userTaskDetail?.workflow_instance?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    );

    const isFirstUserTaskName = userTaskDetail?.workflow_instance?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    );

    const groupedByTaskKey: Record<string, UserTaskEntity[]> = (userTaskDetail?.workflow_instance?.user_tasks || [])
        .filter((task) => task.status !== TaskStatus.IN_PROGRESS)
        .reduce((acc, task) => {
            const key = task.task_key;
            if (!acc[key]) acc[key] = [];
            acc[key].push(task);
            return acc;
        }, {} as Record<string, UserTaskEntity[]>);

    const renderTabContent = () => {
        switch (currentTab) {
            case 'moc-details':
                return (
                    <div className="tab-content">
                        <CollapsibleForm title={isFirstUserTaskName?.task_name || ''} defaultOpen={false}>
                            <div className="tw-px-8 !tw-py-[100px] pb-0">
                                <PreviousFormFields
                                    initialData={userTaskDetail?.workflow_instance?.form_data}
                                    status={TaskStatus.SUBMITTED}
                                    createBy={isFirstForm?.form?.creator?.full_name || ''}
                                    schemaForm={isFirstForm?.form?.schema as FormItem}
                                    dataPlanAndAreaUnit={
                                        userTaskDetail?.workflow_instance?.form_data?.area_of_implementation
                                    }
                                    assignee={isFirstForm?.created_by}
                                    firstAreaOwner={
                                        userTaskDetail?.workflow_instance?.camunda_variables?.firstAreaOwner
                                    }
                                    stepName={isFirstUserTaskName?.task_name}
                                    isFileEntry
                                />
                            </div>
                        </CollapsibleForm>
                        {Object.entries(groupedByTaskKey).map(([taskKey, tasks]) => {
                            const taskName = tasks[0]?.task_name || taskKey;
                            const isFormApproval = isValidFormApproval(tasks);

                            return (
                                <CollapsibleForm key={taskName} title={taskName} defaultOpen={false}>
                                    <div className="tw-px-8 !tw-py-[100px] pb-0 space-y-4">
                                        {isFormApproval ? (
                                            <TableReview data={tasks} />
                                        ) : (
                                            tasks.map((task, index) => {
                                                const isLastOfSameOrder =
                                                    index === tasks.length - 1 ||
                                                    task?.order !== tasks[index + 1]?.order;

                                                const distinctOrders = [...new Set(tasks?.map((item) => item.order))];
                                                const showOrderSeparator = distinctOrders.length > 1;

                                                return (
                                                    <React.Fragment key={index}>
                                                        <PreviousFormFields
                                                            key={`${taskName}-${index}`}
                                                            initialData={task?.form_data}
                                                            status={task.status}
                                                            createBy={task.assigneeInfo?.full_name}
                                                            schemaForm={task?.form?.schema as FormItem}
                                                            dataPlanAndAreaUnit={
                                                                task?.workflow_instance?.form_data
                                                                    ?.area_of_implementation
                                                            }
                                                            assignee={task?.assignee}
                                                            firstAreaOwner={
                                                                userTaskDetail?.workflow_instance?.camunda_variables
                                                                    ?.firstAreaOwner
                                                            }
                                                            stepName={task?.task_name}
                                                            isFileEntry
                                                        />
                                                        {showOrderSeparator && isLastOfSameOrder && (
                                                            <div className="tw-text-lg tw-max-w-[1000px] tw-font-bold tw-mb-8 tw-text-center tw-flex tw-items-center tw-gap-2 tw-mx-auto">
                                                                <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                                                <span className="tw-px-2">
                                                                    {getOrdinalSuffix(task.order)}
                                                                </span>
                                                                <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                                            </div>
                                                        )}
                                                    </React.Fragment>
                                                );
                                            })
                                        )}
                                    </div>
                                </CollapsibleForm>
                            );
                        })}
                    </div>
                );
            case 'comments':
                return (
                    <TaskCommentsSection
                        taskComments={taskComments}
                        onSendComment={onSendComment}
                        onDeleteComment={onDeleteComment}
                        isSendingComment={isSendingComment}
                        userTaskDetail={userTaskDetail}
                        groupedTaskComments={groupedTaskComments}
                    />
                );
            case 'attachments':
                return <TaskAttachmentsSection fileAttachments={fileAttachments} />;
            default:
                return null;
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-dialog-scrollable tw-max-w-[1200px]">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                MOC Details: {userTaskDetail?.workflow_instance?.business_key}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <div className="modal-body">
                            {/* Tabs */}
                            <ul className="nav nav-tabs mb-2" role="tablist">
                                <li className="nav-item">
                                    <button
                                        className={classNames('nav-link cursor-pointer', {
                                            active: currentTab === 'moc-details',
                                        })}
                                        onClick={() => handleTabClick('moc-details')}
                                        role="tab"
                                        aria-selected={currentTab === 'moc-details'}
                                    >
                                        MOC Details
                                    </button>
                                </li>
                                <li className="nav-item">
                                    <button
                                        className={classNames('nav-link cursor-pointer', {
                                            active: currentTab === 'comments',
                                        })}
                                        onClick={() => handleTabClick('comments')}
                                        role="tab"
                                        aria-selected={currentTab === 'comments'}
                                    >
                                        Comments ({taskComments?.length})
                                    </button>
                                </li>
                                <li className="nav-item">
                                    <button
                                        className={classNames('nav-link cursor-pointer', {
                                            active: currentTab === 'attachments',
                                        })}
                                        onClick={() => handleTabClick('attachments')}
                                        role="tab"
                                        aria-selected={currentTab === 'attachments'}
                                    >
                                        Attachments ({fileLength})
                                    </button>
                                </li>
                            </ul>

                            {/* Tab Content */}
                            <div className="tab-content">{renderTabContent()}</div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
