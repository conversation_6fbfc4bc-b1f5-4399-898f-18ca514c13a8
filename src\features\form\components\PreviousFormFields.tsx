/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useMemo } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import PlanAndAreaUnit from './PlanAndAreaUnit';
import SelectGateKeepers from './SelectGateKeepers';
import { FormFieldsCheckboxOption, FormItem } from 'types/Form';
import classNames from 'classnames';
import AimsSystem from './AimsSystem';
import ESmartIsoSystem from './ESmartIsoSystem';
import PhaDatabase from './PhaDatabase';
import FileUpload from './FileUpload';
import { AlertCircle } from 'react-feather';
import { TaskStatus } from 'types/Task';
import { getStatusBadgeStep } from 'utils/common';
import { DateRangePicker as RsuiteDateRangePicker } from 'rsuite';
import 'rsuite/DateRangePicker/styles/index.css';
import 'react-quill/dist/quill.snow.css';
import { UserListAllQuery, UserQueryVariables } from 'types/User';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { QUERY_KEY } from 'constants/common';
import { USER_LIST_ALL } from 'services/UserService';
import { keepPreviousData } from '@tanstack/react-query';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface PreviousFormFieldsProps {
    initialData?: any | null;
    schemaForm: FormItem;
    dataPlanAndAreaUnit?: any;
    assignee?: string;
    firstAreaOwner?: string;
    status: TaskStatus;
    createBy: string;
    stepName?: string;
    isFileEntry?: boolean;
}
export default function PreviousFormFields({
    initialData,
    schemaForm,
    dataPlanAndAreaUnit,
    assignee,
    firstAreaOwner,
    status,
    createBy,
    stepName,
    isFileEntry,
}: PreviousFormFieldsProps) {
    const methods = useForm({
        shouldUnregister: true,
        defaultValues: initialData || {},
    });
    const { reset, unregister, getValues, control, watch, register } = methods;

    const shouldFetchPSSR = useMemo(
        () => schemaForm?.form_fields?.some((item) => item.dataSource === 'pssr_list'),
        [schemaForm?.form_fields]
    );

    const { data: dataPSSR } = useGraphQLQuery<UserListAllQuery, UserQueryVariables>(
        [QUERY_KEY.USERS_LIST_ALL],
        USER_LIST_ALL,
        {
            search: '',
            sort: '',
            filters: ['userAreaRoles.role.type:=(2)'],
        },
        '',
        {
            enabled: shouldFetchPSSR,
            placeholderData: keepPreviousData,
        }
    );

    const dataPssrList: FormFieldsCheckboxOption[] = useMemo(() => {
        if (!dataPSSR?.users_list_all) return [];

        return dataPSSR?.users_list_all?.map((user) => ({
            id: user.id,
            value: user.id,
            label: user.full_name,
        }));
    }, [dataPSSR]);

    useEffect(() => {
        const currentKeys = schemaForm?.form_fields?.map((f) => f.key);
        Object.keys(getValues()).forEach((key) => {
            if (!currentKeys?.includes(key)) {
                unregister(key);
            }
        });
    }, [schemaForm?.form_fields]);

    useEffect(() => {
        if (initialData) {
            reset(initialData);
        }
    }, [reset, initialData, dataPSSR, dataPssrList]);

    const parseDateRangeValue = (value: string): [Date, Date] | null => {
        if (!value || typeof value !== 'string') return null;

        const parts = value.split('-');
        if (parts.length !== 2) return null;

        try {
            const startDate = new Date(parts[0]);
            const endDate = new Date(parts[1]);

            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                return null;
            }

            return [startDate, endDate];
        } catch {
            return null;
        }
    };

    const getFieldColors = (form: any) => ({
        labelColor: form.labelColor || schemaForm?.settings?.fieldLabelColor || '#5e5873',
        backgroundColor: form.backgroundColor || schemaForm?.settings?.formBackgroundColor || 'transparent',
    });

    return (
        <>
            <FormProvider {...methods}>
                <div className="card tw-mb-8 tw-p-6 tw-max-w-[1000px] tw-mx-auto border tw-bg-[white]">
                    <div className="content-body">
                        <div>
                            <div className="tw-flex tw-mb-4 tw-mx-2 tw-flex-wrap">
                                <div className="tw-flex tw-gap-2">
                                    <p className="tw-font-bold">Step:</p> {stepName}
                                </div>
                                <div className="mx-2">|</div>
                                <div className="tw-flex tw-gap-2">
                                    <p className="tw-font-bold">Reviewer Name:</p> {createBy}
                                </div>
                                <div className="mx-2">|</div>
                                <div className="tw-flex tw-gap-2">
                                    <p className="tw-font-bold">Status:</p> {getStatusBadgeStep(status)}
                                </div>
                            </div>
                            {schemaForm?.settings?.title && (
                                <h2 className="card-title tw-mx-2 tw-py-2 tw-border-b tw-border-b-[#e6e6e8] !tw-font-bold">
                                    {schemaForm?.settings?.title}
                                </h2>
                            )}
                            <form>
                                <div
                                    className="tw-flex tw-flex-wrap"
                                    style={{
                                        justifyContent: schemaForm?.settings?.formAlignment,
                                    }}
                                >
                                    {schemaForm?.form_fields?.map((form, index) => {
                                        // Auto-determine typeAppear from keyAppear
                                        const findTypeAppearFromKey = (
                                            keyAppear: string
                                        ): 'checkbox' | 'radio' | 'select' | '' => {
                                            if (!keyAppear || !schemaForm?.form_fields) return '';
                                            const targetField = schemaForm.form_fields.find(
                                                (field) => field.key === keyAppear
                                            );
                                            if (!targetField) return '';
                                            switch (targetField.type) {
                                                case 'checkbox':
                                                    return 'checkbox';
                                                case 'radio':
                                                    return 'radio';
                                                case 'select':
                                                    return 'select';
                                                default:
                                                    return '';
                                            }
                                        };

                                        const actualTypeAppear =
                                            form.keyAppear && findTypeAppearFromKey(form.keyAppear);

                                        const isVisible =
                                            form.keyAppear && form.valueAppear
                                                ? actualTypeAppear === 'checkbox'
                                                    ? watch(form.keyAppear || '', [])?.includes?.(form?.valueAppear)
                                                    : watch(form.keyAppear || '') === form?.valueAppear
                                                : !form.keyAppear;

                                        const isFirstAreaOwner = form?.isFirstAreaOwner;

                                        const camundaVariables = isFirstAreaOwner && firstAreaOwner;

                                        const isAppearFirstAreaOwner =
                                            isFirstAreaOwner &&
                                            assignee &&
                                            ((typeof camundaVariables === 'string' && camundaVariables !== assignee) ||
                                                (Array.isArray(camundaVariables) &&
                                                    !camundaVariables.includes(assignee)));

                                        if (form.type === 'text' || form.type === 'number' || form.type === 'time') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            alignItems: form.isTable ? 'center' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames({
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && !form.isTable && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <input
                                                            className={classNames(
                                                                'form-control tw-pointer-events-none'
                                                            )}
                                                            style={{
                                                                height: `${form.height}px`,
                                                                background: 'transparent',
                                                            }}
                                                            type={form.type}
                                                            placeholder={form.placeholder}
                                                            {...register(form.key)}
                                                        />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'date') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            alignItems: form.isTable ? 'center' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames({
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && !form.isTable && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <Controller
                                                            name={form.key}
                                                            control={control}
                                                            defaultValue={null}
                                                            render={({ field }) => (
                                                                <DatePicker
                                                                    placeholderText="dd/mm/yyyy"
                                                                    onChange={(date) => field.onChange(date)}
                                                                    selected={field.value}
                                                                    dateFormat="dd/MM/yyyy"
                                                                    wrapperClassName={`col-12`}
                                                                    id={form.id}
                                                                    className={classNames(
                                                                        `form-control !tw-bg-transparent tw-pointer-events-none`
                                                                    )}
                                                                    customInput={
                                                                        <input style={{ height: `${form.height}px` }} />
                                                                    }
                                                                />
                                                            )}
                                                        />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'date_range') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        data-id={form.id}
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            alignItems: form.isTable ? 'center' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames({
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && !form.isTable && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <Controller
                                                            name={form.key}
                                                            control={control}
                                                            render={({ field: { value } }) => (
                                                                <div
                                                                    className={classNames(
                                                                        'custom-date-range-wrapper, tw-pointer-events-none tw-bg-transparent'
                                                                    )}
                                                                    style={
                                                                        {
                                                                            height: `${form.height}px`,
                                                                            '--date-range-height': `${form.height}px`,
                                                                        } as React.CSSProperties
                                                                    }
                                                                >
                                                                    <RsuiteDateRangePicker
                                                                        format="dd/MM/yyyy"
                                                                        character=" – "
                                                                        className="form-control new-date-range-picker"
                                                                        value={parseDateRangeValue(value || '')}
                                                                        disabled
                                                                    />
                                                                </div>
                                                            )}
                                                        />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'text_area') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            alignItems: form.isTable ? 'center' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames({
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && !form.isTable && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <textarea
                                                            className={classNames('form-control')}
                                                            style={{
                                                                height: `${form.height}px`,
                                                                maxHeight: '180px',
                                                                background: 'transparent',
                                                            }}
                                                            disabled
                                                            placeholder={form.placeholder}
                                                            {...register(form.key)}
                                                        />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'checkbox') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames('tw-flex tw-flex-col', {
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && !form.isTable && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        {form?.option?.map((cb) => (
                                                            <div className="form-check tw-mb-[4px]" key={cb.id}>
                                                                <input
                                                                    className={classNames(
                                                                        'form-check-input tw-pointer-events-none'
                                                                    )}
                                                                    type="checkbox"
                                                                    value={cb.value}
                                                                    {...register(form.key)}
                                                                />
                                                                <label
                                                                    className={classNames(
                                                                        'form-check-label tw-pointer-events-none'
                                                                    )}
                                                                >
                                                                    {cb.label}
                                                                </label>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'radio') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            // alignItems: form.isTable ? 'center' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames('tw-flex tw-flex-col', {
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && !form.isTable && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        {form?.option?.map((cb) => (
                                                            <div
                                                                className={classNames(
                                                                    'form-check tw-mb-[4px] tw-pointer-events-none'
                                                                )}
                                                                key={cb.id}
                                                            >
                                                                <input
                                                                    className={classNames(
                                                                        'form-check-input tw-pointer-events-none'
                                                                    )}
                                                                    type="radio"
                                                                    value={cb.value}
                                                                    {...register(form.key)}
                                                                />
                                                                <label
                                                                    className={classNames(
                                                                        'form-check-label tw-pointer-events-none'
                                                                    )}
                                                                >
                                                                    {cb.label}
                                                                </label>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'select') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            alignItems: form.isTable ? 'center' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable
                                                                ? '#f3f2f7'
                                                                : getFieldColors(form).backgroundColor,
                                                        }}
                                                        className={classNames('tw-flex tw-flex-col', {
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <select
                                                            className={classNames('form-select tw-pointer-events-none')}
                                                            style={{ height: `${form.height}px` }}
                                                            key={form.id}
                                                            {...register(form.key)}
                                                        >
                                                            <option value="">-- Please select --</option>
                                                            {form.dataSource === 'custom'
                                                                ? form?.option?.map((opt) => (
                                                                      <option key={opt.id} value={opt.value}>
                                                                          {opt.label}
                                                                      </option>
                                                                  ))
                                                                : dataPssrList?.map((opt) => (
                                                                      <option key={opt.id} value={opt.value}>
                                                                          {opt.label}
                                                                      </option>
                                                                  ))}
                                                        </select>
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'free_text') {
                                            return (
                                                isVisible &&
                                                !isAppearFirstAreaOwner && (
                                                    <div
                                                        data-id={form.id}
                                                        key={index}
                                                        style={{
                                                            width: `${
                                                                form.isTable
                                                                    ? `calc(${form.width}% - 0.5px)`
                                                                    : `${form.width}%`
                                                            }`,
                                                            height: 'auto',
                                                            paddingInline: '6px',
                                                            paddingBlock: '4px',

                                                            margin: form.isTable ? '0.5px' : '',
                                                            display: form.isTable ? 'flex' : '',
                                                            justifyContent: form.isTable ? 'center' : '',
                                                            background: form.isTable ? '#f3f2f7' : '',
                                                        }}
                                                        className={classNames('tw-flex tw-flex-col ql-editor', {
                                                            'tw-hidden': isAppearFirstAreaOwner,
                                                        })}
                                                        dangerouslySetInnerHTML={{ __html: form.label || '' }}
                                                    ></div>
                                                )
                                            );
                                        }
                                        if (form.type === 'area_of_implementation') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                        }}
                                                        className="tw-flex tw-flex-col"
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <PlanAndAreaUnit control={control} name={form.key} disabled />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'select_gate_keepers') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                        }}
                                                        className="tw-flex tw-flex-col"
                                                    >
                                                        {form.label && (
                                                            <label className="form-label tw-text-[14px]  tw-font-bold">
                                                                {form.label}{' '}
                                                                {form.isRequired && (
                                                                    <span className="tw-text-red-500 tw-ml-1">*</span>
                                                                )}
                                                            </label>
                                                        )}
                                                        <SelectGateKeepers
                                                            control={control}
                                                            name={form.key}
                                                            disabled
                                                            dataPlanAndAreaUnit={dataPlanAndAreaUnit}
                                                        />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'aims_system') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                        }}
                                                        className="tw-flex tw-flex-col"
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <AimsSystem control={control} name={form.key} disabled />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'e_smart_iso_system') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                        }}
                                                        className="tw-flex tw-flex-col"
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <ESmartIsoSystem control={control} name={form.key} disabled />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'pha_database') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                        }}
                                                        className="tw-flex tw-flex-col"
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <PhaDatabase control={control} name={form.key} disabled />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'file') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            width: `${form.width}%`,
                                                            paddingInline: '6px',
                                                            paddingBlock: '10px',
                                                        }}
                                                        className={classNames('tw-flex tw-flex-col')}
                                                    >
                                                        {form.label && (
                                                            <div className="tw-mb-[4px] tw-flex tw-items-center">
                                                                <label
                                                                    className="form-label tw-text-[14px] tw-font-bold !tw-mb-[0px]"
                                                                    style={{
                                                                        color: getFieldColors(form).labelColor,
                                                                    }}
                                                                >
                                                                    {form.label}
                                                                    {form.isRequired && (
                                                                        <span className="tw-text-red-500 tw-ml-1">
                                                                            *
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                {form.tooltip && form.tooltip !== `<p><br></p>` && (
                                                                    <div className="tw-relative tw-inline-block tw-group tw-ml-2 ">
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                        >
                                                                            <AlertCircle size={14} />
                                                                        </button>

                                                                        <div
                                                                            className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: form.tooltip || '',
                                                                            }}
                                                                        ></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        <FileUpload name={form.key} isFileEntry={isFileEntry} />
                                                    </div>
                                                )
                                            );
                                        }
                                        if (form.type === 'tooltip') {
                                            return (
                                                isVisible && (
                                                    <div
                                                        className="tw-relative tw-flex tw-items-center"
                                                        style={{
                                                            width: `${form.width}%`,
                                                        }}
                                                        key={index}
                                                    >
                                                        <div className="tw-relative tw-inline-block tw-group">
                                                            <button
                                                                type="button"
                                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                            >
                                                                <AlertCircle size={14} />
                                                            </button>

                                                            <div
                                                                className="header-navbar navbar-shadow tw-bg-white tw-absolute tw-z-10 tw-bottom-full tw-left-1/2 -tw-translate-x-1/2 tw-mb-2  tw-px-3 tw-py-2 tw-rounded tw-opacity-0 group-hover:tw-opacity-100 tw-transition tw-pointer-events-none tw-w-[240px]"
                                                                dangerouslySetInnerHTML={{
                                                                    __html: form.label || '',
                                                                }}
                                                            ></div>
                                                        </div>
                                                    </div>
                                                )
                                            );
                                        }
                                    })}
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </FormProvider>
        </>
    );
}
